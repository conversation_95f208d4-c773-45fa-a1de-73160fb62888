"use client"

import * as React from "react"
import { z } from "zod"
import {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight, ColumnsIcon, Copy, Eye, MoreHorizontal, <PERSON><PERSON><PERSON>, <PERSON>, Trash } from "lucide-react"
import { Newsletter } from "@/types/newsletter"
import { Spinner } from "../ui/shadcn-io/spinner"
import Link from "next/link"
import { formatDate } from "@/lib/utils"

export const newsletterSchema = z.object({
  id: z.string(),
  name: z.string(),
  brand_name: z.string(),
  template_name: z.string(),
  status_display: z.string(),
  created_at: z.string(),
  updated_at: z.string(),
})

interface PaginationProps {
  page: number
  pageSize: number
  totalCount: number
  hasNext: boolean
  hasPrevious: boolean
  onPageChange: (page: number) => void
  onPageSizeChange: (pageSize: number) => void
}

interface NewslettersTableProps {
  data: Newsletter[]
  loading?: boolean
  onRefresh?: () => void
  pagination?: PaginationProps
}

export function NewslettersTable({ data, loading = false, onRefresh, pagination }: NewslettersTableProps) {
  const [rowSelection, setRowSelection] = React.useState({})
  const [columnVisibility, setColumnVisibility] = React.useState<VisibilityState>({})
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([])
  const [sorting, setSorting] = React.useState<SortingState>([])

  const columns: ColumnDef<Newsletter>[] = [
        {
      accessorKey: "brand_name",
      header: "Marca",
      cell: ({ row }) => (
        <div className="pl-2">
          {row.getValue("brand_name")}
        </div>
      ),
    },
    {
      accessorKey: "name",
      header: "Nom de la newsletter",
      cell: ({ row }) => (
        <div >{row.getValue("name")}</div>
      ),
    },
    {
      accessorKey: "template_name",
      header: "Plantilla",
      cell: ({ row }) => (
        <div className="text-sm">{row.getValue("template_name")}</div>
      ),
    },
    {
      id: "languages",
      header: "Idiomes",
      cell: ({ row }) => {
        const newsletter = row.original
        return (
          <div className="flex flex-wrap gap-1">
            {newsletter.languages.map((lang) => (
              <Badge key={lang.id} variant="secondary" className="text-xs">
                {lang.language_display}
              </Badge>
            ))}
          </div>
        )
      },
    },
    {
      accessorKey: "status_display",
      header: "Estat",
      cell: ({ row }) => {
        const status = row.getValue("status_display") as string
        return (
          <Badge
            variant={status === "Publicat" ? "default" : "secondary"}
            className="px-2 py-1"
          >
            {status}
          </Badge>
        )
      },
    },
    {
      id: "created_by",
      header: "Creat per",
      cell: ({ row }) => {
        const newsletter = row.original
        return (
          <div className="text-sm">
            {newsletter.created_by?.first_name} {newsletter.created_by?.last_name}
          </div>
        )
      },
    },
    {
      accessorKey: "created_at",
      header: "Creat el",
      cell: ({ row }) => {
        const date = new Date(row.getValue("created_at"))
        return <div className="text-sm">{formatDate(row.getValue("created_at") as string)}</div>
      },
    },
  ]
  const actionsColumn: ColumnDef<Newsletter>[] = [
    ...columns,
    {
      id: "actions",
      enableHiding: false,
      cell: ({ row }) => {
        const newsletter = row.original

        return (
            <Link href={`/newsletter/builder/${newsletter.id}`}>

          <Button variant="ghost" size={"icon"} className="h-8 w-8 p-0" onClick={() => {}}>
            <Pencil className="h-4 w-4" />
          </Button>
            </Link>  
        )
      },
    }
  ]


  const table = useReactTable({
    data,
    columns: actionsColumn,
    state: {
      sorting,
      columnVisibility,
      rowSelection,
      columnFilters,
    },
    getRowId: (row) => row.id,
    enableRowSelection: true,
    onRowSelectionChange: setRowSelection,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onColumnVisibilityChange: setColumnVisibility,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
  })

  if (loading) {
    return (
      <div className="p-6 space-y-6">
        <div className="flex items-center justify-center h-64">
          <div className="flex flex-col items-center gap-2 mb-4">
            <div>
              <Spinner key="infinite" variant="infinite" size={64} />
            </div>
            <div className="text-center animate-pulse">
              Carregant newsletters...
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        {/* <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm">
              <ColumnsIcon className="mr-2 h-4 w-4" />
              Columnes
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            {(() => {
              const columnNames: Record<string, string> = {
                name: "Nom del butlletí",
                brand_name: "Marca",
                language_display: "Idioma",
                subject: "Assumpte",
                status_display: "Estat",
                created_at: "Creat el",
                updated_at: "Actualitzat el",
              };
              return table
                .getAllColumns()
                .filter((column) => column.getCanHide())
                .map((column) => {
                  const displayName = columnNames[column.id] || column.id;
                  return (
                    <DropdownMenuCheckboxItem
                      key={column.id}
                      className="capitalize"
                      checked={column.getIsVisible()}
                      onCheckedChange={(value) =>
                        column.toggleVisibility(!!value)
                      }
                    >
                      {displayName}
                    </DropdownMenuCheckboxItem>
                  );
                });
            })()}
          </DropdownMenuContent>
        </DropdownMenu> */}
      </div>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header, index) => {
                  return (
                    <TableHead key={header.id} className={index === 0 ? "pl-4" : ""}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                    </TableHead>
                  )
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={actionsColumn.length}
                  className="h-24 text-center"
                >
                  No s'han trobat newsletters.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {/* Pagination Controls */}
      {pagination && (
        <div className="flex items-center justify-between space-x-2 py-4">
          <div className="flex-1 text-sm text-muted-foreground">
            Mostrant {((pagination.page - 1) * pagination.pageSize) + 1} a{" "}
            {Math.min(pagination.page * pagination.pageSize, pagination.totalCount)} de{" "}
            {pagination.totalCount} entrades
          </div>
          <div className="flex items-center space-x-6 lg:space-x-8">
            <div className="flex items-center space-x-2">
              <p className="text-sm font-medium">Files per pàgina</p>
              <select
                value={pagination.pageSize}
                onChange={(e) => {
                  pagination.onPageSizeChange(Number(e.target.value))
                  pagination.onPageChange(1) // Reset to first page when changing page size
                }}
                className="h-8 w-[70px] rounded border border-input bg-background px-3 py-1 text-sm ring-offset-background focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
              >
                {[10, 20, 30, 40, 50].map((pageSize) => (
                  <option key={pageSize} value={pageSize}>
                    {pageSize}
                  </option>
                ))}
              </select>
            </div>
            <div className="flex w-[100px] items-center justify-center text-sm font-medium">
              Pàgina {pagination.page} de{" "}
              {Math.ceil(pagination.totalCount / pagination.pageSize)}
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                className="hidden h-8 w-8 p-0 lg:flex"
                onClick={() => pagination.onPageChange(1)}
                disabled={!pagination.hasPrevious}
              >
                <span className="sr-only">Anar a la primera pàgina</span>
                <ChevronsLeft className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                className="h-8 w-8 p-0"
                onClick={() => pagination.onPageChange(pagination.page - 1)}
                disabled={!pagination.hasPrevious}
              >
                <span className="sr-only">Anar a la pàgina anterior</span>
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                className="h-8 w-8 p-0"
                onClick={() => pagination.onPageChange(pagination.page + 1)}
                disabled={!pagination.hasNext}
              >
                <span className="sr-only">Anar a la pàgina següent</span>
                <ChevronRight className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                className="hidden h-8 w-8 p-0 lg:flex"
                onClick={() => pagination.onPageChange(Math.ceil(pagination.totalCount / pagination.pageSize))}
                disabled={!pagination.hasNext}
              >
                <span className="sr-only">Anar a l'última pàgina</span>
                <ChevronsRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
