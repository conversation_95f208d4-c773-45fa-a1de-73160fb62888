"use client"

import * as React from "react"
import { useMemo, useEffect, useRef } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Eye, Globe } from "lucide-react"
import { cn } from "@/lib/utils"
import { NewsletterBlock, NewsletterHeaderFooter } from "@/types/newsletter"
import { useLanguageContext } from "@/contexts/language-context"

interface HTMLPreviewProps {
  blocks: NewsletterBlock[]
  headers?: NewsletterHeaderFooter[]
  footers?: NewsletterHeaderFooter[]
  className?: string
}

export function HTMLPreview({ blocks, headers = [], footers = [], className }: HTMLPreviewProps) {
  const { selectedLanguage, setSelectedLanguage, languages, loading: languagesLoading } = useLanguageContext()
  const iframeRef = useRef<HTMLIFrameElement>(null)

  // Build the complete HTML preview
  const previewHtml = useMemo(() => {
    const safeHeaders = headers || []
    const safeBlocks = blocks || []
    const safeFooters = footers || []

    let html = ""

    // Add headers first
    for (const header of safeHeaders) {
      let content = header.html_content || ""

      // Replace variables with their values in the selected language
      if (header.variable_values && Array.isArray(header.variable_values)) {
        header.variable_values.forEach((variable) => {
          const regex = new RegExp(`\\{\\{\\s*${variable.name}\\s*\\}\\}`, 'g')
          const languageValue = variable.value[selectedLanguage as keyof typeof variable.value] || ''
          content = content.replace(regex, languageValue)
        })
      }

      html += `\n<!-- Header: ${header.name} -->\n`
      html += content + "\n"
    }

    // Add visible blocks sorted by order_position
    const visibleBlocks = safeBlocks
      .filter(b => b?.is_visible)
      .sort((a, b) => (a?.order_position || 0) - (b?.order_position || 0))

    for (const block of visibleBlocks) {
      let content = block.html_content || ""

      // Replace variables with their values in the selected language
      if (block.variable_values && Array.isArray(block.variable_values)) {
        block.variable_values.forEach((variable) => {
          const regex = new RegExp(`\\{\\{\\s*${variable.name}\\s*\\}\\}`, 'g')
          const languageValue = variable.value[selectedLanguage as keyof typeof variable.value] || ''
          content = content.replace(regex, languageValue)
        })
      }

      html += `\n<!-- Block: ${block.name} (#${block.order_position}) -->\n`
      html += content + "\n"
    }

    // Add footers last
    for (const footer of safeFooters) {
      let content = footer.html_content || ""

      // Replace variables with their values in the selected language
      if (footer.variable_values && Array.isArray(footer.variable_values)) {
        footer.variable_values.forEach((variable) => {
          const regex = new RegExp(`\\{\\{\\s*${variable.name}\\s*\\}\\}`, 'g')
          const languageValue = variable.value[selectedLanguage as keyof typeof variable.value] || ''
          content = content.replace(regex, languageValue)
        })
      }

      html += `\n<!-- Footer: ${footer.name} -->\n`
      html += content + "\n"
    }

    return html
  }, [blocks, headers, footers, selectedLanguage])

  // Update iframe content when previewHtml changes
  useEffect(() => {
    if (iframeRef.current && previewHtml) {
      const iframeDoc = iframeRef.current.contentDocument || iframeRef.current.contentWindow?.document
      if (iframeDoc) {
        iframeDoc.open()
        iframeDoc.write(`
          <!DOCTYPE html>
          <html>
            <head>
              <meta charset="UTF-8">
              <meta name="viewport" content="width=device-width, initial-scale=1.0">
              <style>
                /* Reset default styles to prevent any browser defaults */
                * {
                  margin: 0;
                  padding: 0;
                  box-sizing: border-box;
                }
                html, body {
                  margin: 0;
                  padding: 0;
                  width: 100%;
                  height: 100%;
                }
              </style>
            </head>
            <body>
              ${previewHtml}
            </body>
          </html>
        `)
        iframeDoc.close()
      }
    }
  }, [previewHtml])

  return (
    <Card className={cn("h-full flex flex-col", className)}>
      <CardContent className="rounded-lg m-4 h-full">
        <div className="bg-white min-w-[320px] overflow-y-auto h-full">
          {
            previewHtml ? (
              <iframe
                ref={iframeRef}
                className="w-full h-full border-0"
                title="HTML Preview"
                sandbox="allow-same-origin"
              />
            ) : (
              <div className="text-sm text-muted-foreground">No hi ha blocs per mostrar</div>
            )
          }
        </div>
      </CardContent>
    </Card>
  )
}
