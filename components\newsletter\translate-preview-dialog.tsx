"use client"

import * as React from "react"
import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Badge } from "@/components/ui/badge"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Languages, Globe, Check, RotateCcw, X, Eye, Space } from "lucide-react"
import { NewsletterBuilderData } from "@/types/newsletter"
import { HTMLPreview } from "./html-preview"
import { LanguageProvider } from "@/contexts/language-context"

interface TranslatePreviewDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  translatedNewsletter: NewsletterBuilderData | null
  sourceLanguage: string
  userInput: string
  onReplace: () => void
  onRedo: () => void
  onCancel: () => void
}

export function TranslatePreviewDialog({
  open,
  onOpenChange,
  translatedNewsletter,
  sourceLanguage,
  userInput,
  onReplace,
  onRedo,
  onCancel
}: TranslatePreviewDialogProps) {
  const [selectedLanguage, setSelectedLanguage] = useState('ca')

  const getLanguageDisplay = (langCode: string) => {
    const languageMap: Record<string, { display: string; color: string }> = {
      'es': { display: 'Espanyol', color: 'text-red-500' },
      'ca': { display: 'Català', color: 'text-yellow-500' },
      'fr': { display: 'Francès', color: 'text-blue-500' },
      'en': { display: 'Anglès', color: 'text-green-500' }
    }
    return languageMap[langCode] || { display: langCode.toUpperCase(), color: 'text-gray-500' }
  }

  // Get available languages from the newsletter
  const availableLanguages = translatedNewsletter?.languages || []

  const handleReplace = () => {
    onReplace()
    onOpenChange(false)
  }

  const handleRedo = () => {
    onRedo()
    onOpenChange(false)
  }

  const handleCancel = () => {
    onCancel()
    onOpenChange(false)
  }

  if (!translatedNewsletter) {
    return null
  }

  const { display: sourceLanguageDisplay, color: sourceLanguageColor } = getLanguageDisplay(sourceLanguage)

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[900px] max-h-[90vh]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Eye className="h-5 w-5 text-blue-600" />
            Previsualització de la Traducció
          </DialogTitle>
          <DialogDescription>
            Revisa el contingut traduït abans d'aplicar els canvis a la newsletter.
          </DialogDescription>
        </DialogHeader>

        <ScrollArea className="max-h-[calc(90vh-180px)] pr-4">

        <div className="space-y-4">
          {/* Translation Info */}
          <div className="flex items-center gap-4 p-3 bg-muted rounded-lg">
            <div className="flex items-center gap-2">
              <Globe className={`h-4 w-4 ${sourceLanguageColor}`} />
              <span className="font-medium">Traduït des de: {sourceLanguageDisplay}</span>
            </div>
            {userInput && (
              <div className="flex-1">
                <Badge variant="outline" className="text-xs">
                  Context: {userInput.length > 50 ? `${userInput.substring(0, 50)}...` : userInput}
                </Badge>
              </div>
            )}
          </div>

          {/* Language Selector */}
          <div className="flex items-center gap-2">
            <Languages className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm font-medium">Idioma de previsualització:</span>
            <Select value={selectedLanguage} onValueChange={setSelectedLanguage}>
              <SelectTrigger className="w-40">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {availableLanguages.map((lang) => (
                  <SelectItem key={lang.language} value={lang.language}>
                    <span className="flex items-center gap-2">
                      <Globe className={`h-3 w-3 ${getLanguageDisplay(lang.language).color}`} />
                      {getLanguageDisplay(lang.language).display}
                    </span>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Newsletter Preview */}
          <div className="h-[600px] w-full border rounded-lg">
            <LanguageProvider key={selectedLanguage} defaultLanguage={selectedLanguage}>
              <HTMLPreview
                blocks={translatedNewsletter.nl_blocks}
                headers={translatedNewsletter.headers}
                footers={translatedNewsletter.footers}
              />
            </LanguageProvider>
          </div>
        </div>
        </ScrollArea>

        {/* </DialogContent> */}

        <DialogFooter className="flex justify-between">
          <Button
            type="button"
            variant="outline"
            color="destructive"
            onClick={handleCancel}
            className="flex items-center gap-2"
          >
            <X className="h-4 w-4" />
            Cancel·lar
          </Button>

          <div className="flex gap-2">
            <Button
              type="button"
              variant="outline"
              onClick={handleRedo}
              className="flex items-center gap-2"
            >
              <RotateCcw className="h-4 w-4" />
              Tornar a traduir
            </Button>
            <Button
              type="button"
              onClick={handleReplace}
              className="bg-green-600 hover:bg-green-700 flex items-center gap-2"
            >
              <Check className="h-4 w-4" />
              Aplicar Traducció
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
