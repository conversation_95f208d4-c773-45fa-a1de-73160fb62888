"use client"

import * as React from "react"
import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Badge } from "@/components/ui/badge"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Bot, Globe, Check, RotateCcw, X, Eye, Languages } from "lucide-react"
import { NewsletterBuilderData } from "@/types/newsletter"
import { HTMLPreview } from "./html-preview"
import { LanguageProvider } from "@/contexts/language-context"

interface AINewsletterPreviewDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  generatedNewsletter: NewsletterBuilderData | null
  targetLanguage: string
  prompt: string
  onReplace: () => void
  onRedo: () => void
  onCancel: () => void
}

export function AINewsletterPreviewDialog({
  open,
  onOpenChange,
  generatedNewsletter,
  targetLanguage,
  prompt,
  onReplace,
  onRedo,
  onCancel
}: AINewsletterPreviewDialogProps) {
  const [selectedLanguage, setSelectedLanguage] = useState('ca')

  // Set initial language based on target language
  React.useEffect(() => {
    if (targetLanguage) {
      setSelectedLanguage(targetLanguage)
    }
  }, [targetLanguage])

  const getLanguageDisplay = (langCode: string) => {
    const languageMap: Record<string, { display: string; color: string }> = {
      'es': { display: 'Espanyol', color: 'text-red-500' },
      'ca': { display: 'Català', color: 'text-yellow-500' },
      'fr': { display: 'Francès', color: 'text-blue-500' },
      'en': { display: 'Anglès', color: 'text-green-500' }
    }
    return languageMap[langCode] || { display: langCode.toUpperCase(), color: 'text-gray-500' }
  }

  // Get available languages from the newsletter
  const availableLanguages = generatedNewsletter?.languages || []

  const handleReplace = () => {
    onReplace()
    onOpenChange(false)
  }

  const handleRedo = () => {
    onRedo()
    onOpenChange(false)
  }

  const handleCancel = () => {
    onCancel()
    onOpenChange(false)
  }

  if (!generatedNewsletter) {
    return null
  }

  const { display: targetLanguageDisplay, color: targetLanguageColor } = getLanguageDisplay(targetLanguage)

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[900px] max-h-[90vh]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Bot className="h-5 w-5 text-primary" />
            Previsualització del contingut generat per IA
          </DialogTitle>
          <DialogDescription>
            Revisa el contingut generat per la IA abans d'aplicar els canvis a la newsletter.
          </DialogDescription>
        </DialogHeader>

<ScrollArea className="max-h-[calc(90vh-180px)] pr-4">


        <div className="space-y-4">
          {/* Generation Info */}
          <div className="flex items-center gap-4 p-3 bg-muted rounded-lg">
            <div className="flex items-center gap-2">
              <Bot className={`h-4 w-4 ${targetLanguageColor}`} />
              <span className="font-medium">Generat en: {targetLanguageDisplay}</span>
            </div>
            {prompt && (
              <div className="flex-1">
                <Badge variant="outline" className="text-xs">
                  Instruccions: {prompt.length > 50 ? `${prompt.substring(0, 50)}...` : prompt}
                </Badge>
              </div>
            )}
          </div>

          {/* Language Selector */}
          <div className="flex items-center gap-2">
            <Languages className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm font-medium">Idioma de previsualització:</span>
            <Select value={selectedLanguage} onValueChange={setSelectedLanguage}>
              <SelectTrigger className="w-40">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {availableLanguages.map((lang) => (
                  <SelectItem key={lang.language} value={lang.language}>
                    <span className="flex items-center gap-2">
                      <Globe className={`h-3 w-3 ${getLanguageDisplay(lang.language).color}`} />
                      <span>
                        {getLanguageDisplay(lang.language).display}
                      </span>
                    </span>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Newsletter Preview */}
          <div className="h-[600px] w-full border rounded-lg">
            <LanguageProvider key={selectedLanguage} defaultLanguage={selectedLanguage}>
              <HTMLPreview
                blocks={generatedNewsletter.nl_blocks}
                headers={generatedNewsletter.headers}
                footers={generatedNewsletter.footers}
              />
            </LanguageProvider>
          </div>
        </div>
</ScrollArea>

        <DialogFooter className="flex justify-between">
          <Button
            type="button"
            variant="outline"
            onClick={handleCancel}
            className="flex items-center gap-2"
          >
            <X className="h-4 w-4" />
            Cancel·lar
          </Button>

          <div className="flex gap-2">
            <Button
              type="button"
              variant="outline"
              onClick={handleRedo}
              className="flex items-center gap-2"
            >
              <RotateCcw className="h-4 w-4" />
              Generar de nou
            </Button>
            <Button
              type="button"
              onClick={handleReplace}
              className="bg-gradient-to-r from-purple-500 to-blue-500 hover:from-purple-600 hover:to-blue-600 flex items-center gap-2"
            >
              <Check className="h-4 w-4" />
              Aplicar contingut
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
