export interface User {
  id: string
  first_name: string
  last_name: string
  email: string
  updated_at_platform: string
  created_at_platform: string
}

export interface VariableType {
  id: string
  name: string
  display_name: string
  description: string
  field_type: string
  field_type_display: string
  ai_generated: boolean
  max_length: number | null
}

export interface BlockVariable {
  id: string
  block: string
  block_name: string
  variable_type: string
  name: string
  default_value: {
    es: string
    ca: string
    fr: string
    en: string
  }
}

export interface Block {
  id: string
  name: string
  brand: string
  brand_name: string
  description: string
  html_content: string
  variables: BlockVariable[]
  created_by: User
  updated_by: User
  created_at: string
  updated_at: string
  is_active?: boolean
  html_content_rendered?: string
}

export interface BlockResponse {
  count: number
  next: string | null
  previous: string | null
  results: Block[]
}

export interface HeaderFooterVariable {
  id: string
  header_footer: string
  header_footer_name: string
  variable_type_id: string
  name: string
  default_value: {
    es: string
    ca: string
    fr: string
    en: string
  }
  variable_type: string
}

export interface HeaderFooter {
  id: string
  name: string
  brand: string
  brand_name: string
  element_type: string
  element_type_display: string
  html_content: string
  variables: HeaderFooterVariable[]
  is_active: boolean
  created_by: User
  updated_by: User
  created_at: string
  updated_at: string
}

export interface HeaderFooterResponse {
  count: number
  next: string | null
  previous: string | null
  results: HeaderFooter[]
}
