"use client"

import * as React from "react"
import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  ChevronDown,
  ChevronRight,
  ArrowUp,
  ArrowDown,
  Sparkles,
  GripVertical,
  Languages,
} from "lucide-react"
import { cn } from "@/lib/utils"
import { NewsletterHeaderFooter } from "@/types/newsletter"
import { useLanguageContext } from "@/contexts/language-context"

interface HeaderFooterItemProps {
  headerFooter: NewsletterHeaderFooter
  isExpanded: boolean
  onToggle: () => void
  onVariableChange?: (headerFooterId: string, variableId: string, language: string, value: string) => void
  onHtmlContentChange?: (headerFooterId: string, htmlContent: string) => void
  onAIClick?: () => void
  onTranslateClick?: () => void
  type: 'header' | 'footer'
  isDragging?: boolean
  isDragDisabled?: boolean
}

export function HeaderFooterItem({
  headerFooter,
  isExpanded,
  onToggle,
  onVariableChange,
  onHtmlContentChange,
  onAIClick,
  onTranslateClick,
  type,
  isDragging,
  isDragDisabled = false
}: HeaderFooterItemProps) {
  const { selectedLanguage } = useLanguageContext()
  const { variableTypes, loading: variableTypesLoading } = useVariableTypes()
  const isHeader = type === 'header'

  // Helper function to check if variable is "src imatge" type
  const isSrcImatgeVariable = (variable: any) => {
    return variable.variable_type_display_name === 'src imatge'
  }

  // Function to console log content for src imatge variables
  const handleSyncSrcImatgeVariable = (variable: any) => {
    const currentLanguageValue = variable.value[selectedLanguage] || ''

    console.log('Sync button clicked for src imatge variable:')
    console.log('Variable name:', variable.name)
    console.log('Current language:', selectedLanguage)
    console.log('Current content:', currentLanguageValue)
    console.log('All language values:', variable.value)

    toast.success(`Content logged to console for variable: ${variable.name}`)
  }

  return (
    <Card className={cn("transition-colors", isDragging && "opacity-80")}>
      <Collapsible open={isExpanded}>
        <CollapsibleTrigger asChild>
          <CardHeader
            className="cursor-pointer hover:bg-muted/50 transition-colors"
            onClick={onToggle}
          >
            <div className="flex items-center gap-3">
              {/* Drag Handle */}
              <div className="flex-shrink-0">
                <GripVertical className={cn(
                  "h-4 w-4",
                  isDragDisabled
                    ? "text-muted-foreground/40 cursor-not-allowed"
                    : "text-black cursor-grab active:cursor-grabbing"
                )} />
              </div>

              {/* Expand/Collapse Icon */}
              <div className="flex-shrink-0">
                {isExpanded ? (
                  <ChevronDown className="h-4 w-4" />
                ) : (
                  <ChevronRight className="h-4 w-4" />
                )}
              </div>

              {/* Header/Footer Info */}
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2">
                  <CardTitle className="text-sm truncate">{headerFooter.name}</CardTitle>
                  <div className="flex items-center gap-1">
                    <Badge variant="outline" className="text-xs">
                      {headerFooter.element_type_display}
                    </Badge>
                  </div>
                </div>
                <CardDescription className="text-xs truncate">
                  {isHeader ? "Capçalera de la newsletter" : "Peu de pàgina de la newsletter"}
                </CardDescription>
              </div>

              {/* Action Buttons */}
              <div className="flex items-center gap-2">
                {/* Translate Button */}
                {/* <Button
                  variant="outline"
                  size="icon"
                  className="text-blue-600 hover:text-blue-700 hover:bg-blue-50 border-blue-200 hover:border-blue-300"
                  onClick={(e) => {
                    e.stopPropagation()
                    onTranslateClick?.()
                  }}
                >
                  <Languages className="h-4 w-4" />
                </Button> */}

                {/* AI Button */}
                {/* {onAIClick && (
                  <Button
                    variant="outline"
                    size="icon"
                    className="flex shadow-lg text-yellow-400 transition-all duration-300 hover:bg-gradient-to-r hover:from-purple-500 hover:to-blue-500 hover:shadow-xl hover:shadow-blue-500/30 hover:scale-110 group animate-pulse-gradient hover:text-white"
                    onClick={(e) => {
                      e.stopPropagation()
                      onAIClick()
                    }}
                  >
                    <Sparkles className="h-6 w-6 group-hover:animate-pulse" />
                  </Button>
                )} */}
              </div>
            </div>
          </CardHeader>
        </CollapsibleTrigger>

        <CollapsibleContent>
          <CardContent className="pt-0">
            <Separator className="mb-4" />

            {/* Header/Footer Details */}
            <div className="space-y-3">
              <div>
                <h4 className="text-sm font-medium mb-2">Variables</h4>
                {headerFooter.variable_values && headerFooter.variable_values.length > 0 ? (
                  <div className="space-y-4">
                    {(() => {
                      // Sort variables based on their appearance order in the HTML content
                      const sortedVariables = [...headerFooter.variable_values].sort((a, b) => {
                        const htmlContent = headerFooter.html_content || ''
                        const indexA = htmlContent.indexOf(`{{ ${a.name} }}`) !== -1 
                          ? htmlContent.indexOf(`{{ ${a.name} }}`)
                          : htmlContent.indexOf(`{{${a.name}}}`)
                        const indexB = htmlContent.indexOf(`{{ ${b.name} }}`) !== -1
                          ? htmlContent.indexOf(`{{ ${b.name} }}`)
                          : htmlContent.indexOf(`{{${b.name}}}`)
                        
                        // If variable not found in HTML, put it at the end
                        if (indexA === -1 && indexB === -1) return 0
                        if (indexA === -1) return 1
                        if (indexB === -1) return -1
                        
                        return indexA - indexB
                      })
                      
                      return sortedVariables.map((variable) => (
                      <div key={variable.id} className="border rounded-lg p-3">
                        <div className="flex items-center justify-between mb-2">
                          <div className="font-medium text-sm">
                            {variable.name} ({variable.variable_type_display_name})
                          </div>
                          {/* Sync button for src imatge variables */}
                          {isSrcImatgeVariable(variable) && (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleSyncSrcImatgeVariable(variable)}
                              className="flex items-center gap-1 text-xs h-7"
                            >
                              <Copy className="h-3 w-3" />
                              Sincronitzar
                            </Button>
                          )}
                        </div>
                        <Tabs value={selectedLanguage} className="w-full">
                          <TabsList className="grid w-full grid-cols-4">
                            <TabsTrigger value="es">ES</TabsTrigger>
                            <TabsTrigger value="ca">CA</TabsTrigger>
                            <TabsTrigger value="fr">FR</TabsTrigger>
                            <TabsTrigger value="en">EN</TabsTrigger>
                          </TabsList>
                          {Object.entries(variable.value).map(([lang, value]) => (
                            <TabsContent key={lang} value={lang} className="mt-2">
                              <div className="space-y-1">
                                <Label htmlFor={`${variable.id}-${lang}`} className="text-xs">
                                  {lang.toUpperCase()}
                                </Label>
                                {variable.variable_type_field_type === 'text' ? (
                                  <Textarea
                                    id={`${variable.id}-${lang}`}
                                    value={value || ''}
                                    onChange={(e) => onVariableChange?.(headerFooter.id, variable.id, lang, e.target.value)}
                                    className="min-h-[60px] text-xs"
                                    placeholder={`Introdueix ${variable.variable_type_display_name} en ${lang.toUpperCase()}`}
                                  />
                                ) : (
                                  <Input
                                    id={`${variable.id}-${lang}`}
                                    value={value || ''}
                                    onChange={(e) => onVariableChange?.(headerFooter.id, variable.id, lang, e.target.value)}
                                    className="text-xs"
                                    placeholder={`Introdueix ${variable.variable_type_display_name} en ${lang.toUpperCase()}`}
                                  />
                                )}
                              </div>
                            </TabsContent>
                          ))}
                        </Tabs>
                      </div>
                      ))
                    })()}
                  </div>
                ) : (
                  <p className="text-xs text-muted-foreground">No s'han definit variables</p>
                )}
              </div>

              <div>
                <h4 className="text-sm font-medium mb-2">Contingut HTML</h4>
                <div className="space-y-2">
                  <Textarea
                    value={headerFooter.html_content || ''}
                    onChange={(e) => onHtmlContentChange?.(headerFooter.id, e.target.value)}
                    className="min-h-[120px] font-mono text-xs"
                    placeholder="Introdueix el contingut HTML per a aquesta capçalera/peu de pàgina"
                  />
                </div>
              </div>
            </div>
          </CardContent>
        </CollapsibleContent>
      </Collapsible>
    </Card>
  )
}
