"use client"

import * as React from "react"
import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Languages, Globe, Send, FileText } from "lucide-react"
import { Spinner } from "../ui/shadcn-io/spinner"
import { useLanguageContext } from "@/contexts/language-context"
import { useTranslateBlockApi, extractVariables } from "@/hooks/use-translate-block-api"
import { NewsletterBlock } from "@/types/newsletter"
import { useVariableTypes } from "@/hooks"

interface TranslateBlockDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  block: NewsletterBlock | null
  onTranslateResponse: (response: any, sourceLanguage: string) => void
}

export function TranslateBlockDialog({
  open,
  onOpenChange,
  block,
  onTranslateResponse
}: TranslateBlockDialogProps) {
  const { selectedLanguage, languages } = useLanguageContext()
  const { translateBlock, loading: apiLoading, error: apiError } = useTranslateBlockApi()
  const [context, setContext] = useState("")
  const [sourceLanguage, setSourceLanguage] = useState("")

  const { variableTypes, loading: variableTypesLoading } = useVariableTypes()
  const isVariableTypeAI = (variableType: string) => {
    console.group("Detecting AI Variable Type")
    console.log("Variable Types:", variableTypes)
    console.log("Checking Variable Type:", variableType)
    console.groupEnd()
    const variableTypeObj = variableTypes.find(type => type.display_name === variableType)
    return variableTypeObj ? variableTypeObj.ai_generated : false
  }

  // Set default source language when dialog opens
  React.useEffect(() => {
    if (open && !sourceLanguage) {
      setSourceLanguage(selectedLanguage)
    }
  }, [open, selectedLanguage, sourceLanguage])

  const getLanguageDisplay = (langCode: string) => {
    const languageMap: Record<string, { display: string; color: string }> = {
      'es': { display: 'Espanyol', color: 'text-red-500' },
      'ca': { display: 'Català', color: 'text-yellow-500' },
      'fr': { display: 'Francès', color: 'text-blue-500' },
      'en': { display: 'Anglès', color: 'text-green-500' }
    }
    return languageMap[langCode] || { display: langCode.toUpperCase(), color: 'text-gray-500' }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (apiLoading || !sourceLanguage || !block) return

    try {
      // Extract variables for the source language
      const variables = extractVariables(block, sourceLanguage)

      const response = await translateBlock({
        block_id: block.id,
        source_language: sourceLanguage,
        variables: variables,
        user_input: context.trim() || undefined
      })

      onTranslateResponse(response, sourceLanguage)
      onOpenChange(false)
      setContext("")
    } catch (error) {
      console.error('Error calling block translation API:', error)
      // Error is already handled by the useTranslateBlockApi hook
    }
  }

  const handleCancel = () => {
    onOpenChange(false)
    setContext("")
  }

  // Get available source languages from context
  const availableSourceLanguages = languages.map(lang => lang.language)

  // Get variables for preview
  const variables = block && sourceLanguage ? extractVariables(block, sourceLanguage) : {}

  if (!block) {
    return null
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Languages className="h-5 w-5 text-blue-600" />
            Traduir Bloc: {block.name}
          </DialogTitle>
          <DialogDescription>
            Tradueix el contingut del bloc utilitzant intel·ligència artificial.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Source Language Selector */}
          <div className="space-y-2">
            <Label htmlFor="source-language">Idioma d'origen</Label>
            <Select value={sourceLanguage} onValueChange={setSourceLanguage}>
              <SelectTrigger>
                <SelectValue placeholder="Selecciona l'idioma d'origen" />
              </SelectTrigger>
              <SelectContent>
                {availableSourceLanguages.map((langCode) => {
                  const { display, color } = getLanguageDisplay(langCode)
                  return (
                    <SelectItem key={langCode} value={langCode}>
                      <div className="flex items-center gap-2">
                        <Globe className={`h-3 w-3 ${color}`} />
                        <span>{display}</span>
                      </div>
                    </SelectItem>
                  )
                })}
              </SelectContent>
            </Select>
          </div>

          {/* Variables Preview */}
          {Object.keys(variables).length > 0 && (
            <div className="space-y-2">
                <Label className="flex items-center gap-2">
                <FileText className="h-4 w-4" />
                Variables a traduir ({Object.keys(variables).length})
                </Label>
                <ScrollArea className="h-32 w-full border rounded-lg p-3">
                <div className="space-y-2">
                  {Object.entries(variables).map(([variableName, value]) => (
                  <div key={variableName} className="flex items-start gap-2 p-2 bg-muted rounded text-sm">
                    <Badge variant="outline" className="text-xs shrink-0">
                    {variableName}
                    </Badge>
                    <p className="text-muted-foreground break-words" style={{ overflowWrap: 'anywhere' }}>
                    {value || <em>Sense contingut</em>}
                    </p>
                  </div>
                  ))}
                </div>
                </ScrollArea>
              <p className="text-xs text-muted-foreground">
                Aquestes són les variables que es traduiran des de {getLanguageDisplay(sourceLanguage).display}.
              </p>
            </div>
          )}

          {/* Context Input */}
          <div className="space-y-2">
            <Label htmlFor="translate-context">Context per a la traducció (opcional)</Label>
            <Textarea
              id="translate-context"
              placeholder="Proporciona context sobre el contingut, audiència objectiu, to desitjat, o qualsevol informació rellevant per a la traducció del bloc..."
              value={context}
              onChange={(e) => setContext(e.target.value)}
              className="min-h-[80px] resize-none"
              disabled={apiLoading}
            />
            <p className="text-xs text-muted-foreground">
              El context ajuda a generar una traducció més precisa i adequada per al teu públic objectiu.
            </p>
          </div>

          {/* Error Display */}
          {apiError && (
            <div className="text-sm text-red-600 bg-red-50 p-3 rounded-md">
              {apiError}
            </div>
          )}

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={handleCancel}
              disabled={apiLoading}
            >
              Cancel·lar
            </Button>
            <Button
              type="submit"
              disabled={apiLoading || !sourceLanguage || Object.keys(variables).length === 0}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {apiLoading ? (
                <>
                  <Spinner className="mr-2 h-4 w-4" variant="default" />
                  Traduint...
                </>
              ) : (
                <>
                  <Send className="mr-2 h-4 w-4" />
                  Traduir Bloc
                </>
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
