"use client"

import * as React from "react"
import {
  CameraIcon,
  ClipboardListIcon,
  CuboidIcon,
  DatabaseIcon,
  FileCodeIcon,
  FileIcon,
  FileTextIcon,
  HelpCircleIcon,
  LayoutDashboardIcon,
  SearchIcon,
  SettingsIcon,
} from "lucide-react"

import { NavMain } from "@/components/sidebar/nav-main"
import { NavSecondary } from "@/components/sidebar/nav-secondary"
import { NavUser } from "@/components/sidebar/nav-user"
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar"
import { useUserPermissions, PERMISSION_CODENAMES } from "@/hooks/use-user-permissions"
import Link from "next/link"

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const { hasPermission } = useUserPermissions()

  const data = {
    user: {
      name: "<PERSON><PERSON><PERSON>",
      email: "<EMAIL>",
      avatar: "/avatars/shadcn.jpg",
    },
    navMain: [
      {
        title: "Inici",
        url: "/",
        icon: LayoutDashboardIcon,
        permission: null, // Always visible
      },
      {
        title: "Plantilles",
        url: "/templates",
        icon: FileCodeIcon,
        permission: PERMISSION_CODENAMES.VIEW_MANAGE_TEMPLATES,
      },
      {
        title: "Configuració",
        url: "/blocks",
        icon: CuboidIcon,
        permission: null, // Custom permission check below
      }
    ].filter(item => {
      if (!item.permission) {
        // For /blocks route, check if user has either permission
        if (item.url === "/blocks") {
          return hasPermission(PERMISSION_CODENAMES.VIEW_MANAGE_BLOCKS) || 
                 hasPermission(PERMISSION_CODENAMES.VIEW_MANAGE_HEADERS_FOOTERS)
        }
        return true
      }
      return hasPermission(item.permission)
    }),
    navClouds: [
      {
        title: "Capture",
        icon: CameraIcon,
        isActive: true,
        url: "#",
        items: [
          {
            title: "Active Proposals",
            url: "#",
          },
          {
            title: "Archived",
            url: "#",
          },
        ],
      },
      {
        title: "Proposal",
        icon: FileTextIcon,
        url: "#",
        items: [
          {
            title: "Active Proposals",
            url: "#",
          },
          {
            title: "Archived",
            url: "#",
          },
        ],
      },
      {
        title: "Prompts",
        icon: FileCodeIcon,
        url: "#",
        items: [
          {
            title: "Active Proposals",
            url: "#",
          },
          {
            title: "Archived",
            url: "#",
          },
        ],
      },
    ],
    navSecondary: [
      {
        title: "Usuaris i rols",
        url: "/configuration",
        icon: SettingsIcon,
        permission: PERMISSION_CODENAMES.VIEW_MANAGE_USERS_ROLES,
      },
    ].filter(item => !item.permission || hasPermission(item.permission)),
    documents: [
      {
        name: "Data Library",
        url: "#",
        icon: DatabaseIcon,
      },
      {
        name: "Reports",
        url: "#",
        icon: ClipboardListIcon,
      },
      {
        name: "Word Assistant",
        url: "#",
        icon: FileIcon,
      },
    ],
  }
  return (
    <Sidebar collapsible="icon" {...props}>
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton
              tooltip="Grandvalira"
              asChild
              className="data-[slot=sidebar-menu-button]:!p-1.5"
            >
              <Link href="/">
                <svg className="h-5 w-5" width="55" height="40" viewBox="0 0 55 40" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M27.2827 0L54.642 40H0L27.2827 0Z" fill="#17A3CC" />
                  <path d="M20.2803 10.229L40.6174 40L0 40L20.2803 10.229Z" fill="#D72027" />
                  <path d="M27.264 20.458L40.6174 40L13.9479 40L27.264 20.458Z" fill="#144886" />
                </svg>
                <span className="text-base font-semibold">Grandvalira</span>
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent>
        <NavMain items={data.navMain} />
        {/* <NavDocuments items={data.documents} /> */}
        <NavSecondary items={data.navSecondary} className="mt-auto" />
      </SidebarContent>
      <SidebarFooter>
        <NavUser user={data.user} />
      </SidebarFooter>
    </Sidebar>
  )
}
